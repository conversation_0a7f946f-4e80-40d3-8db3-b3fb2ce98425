using System.Text.Json.Serialization;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug user account
/// </summary>
public class SmugMugUser
{
    [JsonPropertyName("Name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("NickName")]
    public string NickName { get; set; } = string.Empty;

    [JsonPropertyName("ViewPassHint")]
    public string? ViewPassHint { get; set; }

    [JsonPropertyName("WebUri")]
    public string WebUri { get; set; } = string.Empty;

    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Uris")]
    public SmugMugUris? Uris { get; set; }
}

/// <summary>
/// Contains URIs for related SmugMug resources
/// </summary>
public class SmugMugUris
{
    [JsonPropertyName("Node")]
    public SmugMugUriInfo? Node { get; set; }

    [JsonPropertyName("UserProfile")]
    public SmugMugUriInfo? UserProfile { get; set; }

    [JsonPropertyName("Features")]
    public SmugMugUriInfo? Features { get; set; }
}

/// <summary>
/// Contains URI information for a SmugMug resource
/// </summary>
public class SmugMugUriInfo
{
    [JsonPropertyName("Uri")]
    public string Uri { get; set; } = string.Empty;

    [JsonPropertyName("Locator")]
    public string? Locator { get; set; }

    [JsonPropertyName("UriDescription")]
    public string? UriDescription { get; set; }
}
