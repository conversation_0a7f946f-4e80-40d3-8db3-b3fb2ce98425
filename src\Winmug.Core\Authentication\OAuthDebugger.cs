using Microsoft.Extensions.Logging;
using System.Text;

namespace Winmug.Core.Authentication;

/// <summary>
/// Debug helper for OAuth signature generation
/// </summary>
public static class OAuthDebugger
{
    public static void LogOAuthRequest(
        ILogger logger,
        string httpMethod,
        string url,
        Dictionary<string, string> parameters,
        string consumerSecret,
        string? tokenSecret = null)
    {
        logger.LogDebug("=== OAuth Request Debug ===");
        logger.LogDebug("HTTP Method: {Method}", httpMethod);
        logger.LogDebug("URL: {Url}", url);
        logger.LogDebug("Consumer Secret: {Secret}", MaskSecret(consumerSecret));
        
        if (tokenSecret != null)
        {
            logger.LogDebug("Token Secret: {TokenSecret}", MaskSecret(tokenSecret));
        }

        logger.LogDebug("Parameters:");
        foreach (var param in parameters.OrderBy(p => p.Key))
        {
            var value = param.Key.Contains("secret") || param.Key.Contains("signature") 
                ? MaskSecret(param.Value) 
                : param.Value;
            logger.LogDebug("  {Key} = {Value}", param.Key, value);
        }

        // Generate signature step by step for debugging
        var normalizedUrl = NormalizeUrlForDebug(url);
        logger.LogDebug("Normalized URL: {NormalizedUrl}", normalizedUrl);

        var normalizedParams = NormalizeParametersForDebug(parameters);
        logger.LogDebug("Normalized Parameters: {NormalizedParams}", normalizedParams);

        var signatureBaseString = $"{httpMethod.ToUpperInvariant()}&{OAuthSignatureGenerator.UrlEncode(normalizedUrl)}&{OAuthSignatureGenerator.UrlEncode(normalizedParams)}";
        logger.LogDebug("Signature Base String: {SignatureBaseString}", signatureBaseString);

        var signingKey = $"{OAuthSignatureGenerator.UrlEncode(consumerSecret)}&{OAuthSignatureGenerator.UrlEncode(tokenSecret ?? string.Empty)}";
        logger.LogDebug("Signing Key: {SigningKey}", MaskSecret(signingKey));

        logger.LogDebug("=== End OAuth Debug ===");
    }

    private static string MaskSecret(string secret)
    {
        if (string.IsNullOrEmpty(secret) || secret.Length <= 8)
            return "***";
        
        return secret.Substring(0, 4) + "***" + secret.Substring(secret.Length - 4);
    }

    private static string NormalizeUrlForDebug(string url)
    {
        var uri = new Uri(url);
        return $"{uri.Scheme.ToLowerInvariant()}://{uri.Host.ToLowerInvariant()}{uri.AbsolutePath}";
    }

    private static string NormalizeParametersForDebug(Dictionary<string, string> parameters)
    {
        var sortedParameters = parameters
            .OrderBy(kvp => kvp.Key)
            .ThenBy(kvp => kvp.Value)
            .Select(kvp => $"{OAuthSignatureGenerator.UrlEncode(kvp.Key)}={OAuthSignatureGenerator.UrlEncode(kvp.Value)}")
            .ToArray();
        
        return string.Join("&", sortedParameters);
    }
}
