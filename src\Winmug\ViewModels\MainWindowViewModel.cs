using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Windows;
using Winmug.Core.Authentication;
using Winmug.Core.Services;

namespace Winmug.ViewModels;

/// <summary>
/// ViewModel for the main application window
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly ISmugMugAuthenticationService _authService;
    private readonly ISmugMugApiClient _apiClient;
    private readonly IDownloadManager _downloadManager;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private bool _isAuthenticated;

    [ObservableProperty]
    private string? _userNickname;

    [ObservableProperty]
    private string _authenticationStatus = "Not authenticated";

    [ObservableProperty]
    private string? _targetDirectory;

    [ObservableProperty]
    private bool _isOperationInProgress;

    [ObservableProperty]
    private string _statusMessage = "Ready";

    [ObservableProperty]
    private ObservableCollection<string> _logMessages = new();

    [ObservableProperty]
    private bool _canStartDownload;

    [ObservableProperty]
    private bool _canPauseDownload;

    [ObservableProperty]
    private bool _canResumeDownload;

    [ObservableProperty]
    private bool _canCancelDownload;

    [ObservableProperty]
    private double _overallProgress;

    [ObservableProperty]
    private double _currentFileProgress;

    [ObservableProperty]
    private string _progressText = string.Empty;

    [ObservableProperty]
    private string _downloadSpeed = string.Empty;

    [ObservableProperty]
    private string _estimatedTimeRemaining = string.Empty;

    // Authentication flow state
    private string? _requestToken;
    private string? _requestTokenSecret;
    private string? _authorizationUrl;

    public MainWindowViewModel(
        ISmugMugAuthenticationService authService,
        ISmugMugApiClient apiClient,
        IDownloadManager downloadManager,
        ILogger<MainWindowViewModel> logger)
    {
        _authService = authService;
        _apiClient = apiClient;
        _downloadManager = downloadManager;
        _logger = logger;

        // Subscribe to authentication events
        _authService.AuthenticationStatusChanged += OnAuthenticationStatusChanged;

        // Subscribe to download events
        _downloadManager.ProgressUpdated += OnDownloadProgressUpdated;
        _downloadManager.StatusChanged += OnDownloadStatusChanged;
        _downloadManager.ErrorOccurred += OnDownloadErrorOccurred;

        // Initialize authentication state
        IsAuthenticated = _authService.IsAuthenticated;
        UpdateAuthenticationStatus();
    }

    [RelayCommand]
    private async Task TestOAuthAsync()
    {
        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing OAuth signature generation...";
            AddLogMessage("Starting OAuth diagnostic test...");

            // Create OAuth tester
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Add("User-Agent", "Winmug/1.0");

            var tester = new Winmug.Core.Authentication.OAuthTester(httpClient, _logger);

            // Get credentials from auth service
            var credentials = _authService.Credentials;

            AddLogMessage($"Testing with Consumer Key: {credentials.ConsumerKey.Substring(0, 8)}...");

            var success = await tester.TestRealOAuthRequestAsync(credentials.ConsumerKey, credentials.ConsumerSecret);

            if (success)
            {
                AddLogMessage("✓ OAuth test successful! Credentials and signature generation are working.");
                StatusMessage = "OAuth test successful";
            }
            else
            {
                AddLogMessage("❌ OAuth test failed. Check the logs above for details.");
                StatusMessage = "OAuth test failed";
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OAuth test failed");
            AddLogMessage($"❌ OAuth test error: {ex.Message}");
            StatusMessage = "OAuth test error";
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task InitiateAuthenticationAsync()
    {
        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Initiating authentication...";
            AddLogMessage("Starting SmugMug authentication process...");

            var requestTokenResponse = await _authService.InitiateAuthenticationAsync();

            _requestToken = requestTokenResponse.Token;
            _requestTokenSecret = requestTokenResponse.TokenSecret;
            _authorizationUrl = requestTokenResponse.AuthorizationUrl;

            AddLogMessage($"✓ Request token obtained successfully");
            AddLogMessage($"Authorization URL: {_authorizationUrl}");

            // Try to open the authorization URL in the default browser with better error handling
            bool browserOpened = false;
            try
            {
                var processInfo = new ProcessStartInfo
                {
                    FileName = _authorizationUrl,
                    UseShellExecute = true
                };

                var process = Process.Start(processInfo);
                browserOpened = process != null;

                if (browserOpened)
                {
                    AddLogMessage("✓ Browser opened successfully");
                }
            }
            catch (Exception browserEx)
            {
                _logger.LogWarning(browserEx, "Failed to open browser automatically");
                AddLogMessage($"⚠ Could not open browser automatically: {browserEx.Message}");
            }

            if (!browserOpened)
            {
                // Fallback: Show the URL to the user
                AddLogMessage("Please manually copy and paste this URL into your browser:");
                AddLogMessage(_authorizationUrl);

                // Try to copy to clipboard
                try
                {
                    Clipboard.SetText(_authorizationUrl);
                    AddLogMessage("✓ URL copied to clipboard");
                }
                catch
                {
                    // Ignore clipboard errors
                }
            }

            StatusMessage = "Please log in to SmugMug in your browser and enter the verification code.";
            AddLogMessage("");
            AddLogMessage("INSTRUCTIONS:");
            AddLogMessage("1. Log in to SmugMug with your username and password");
            AddLogMessage("2. Click 'Authorize' to grant access to Winmug");
            AddLogMessage("3. Copy the 6-digit verification code");
            AddLogMessage("4. Paste it in the field below and click 'Complete Authentication'");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initiate authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease check your internet connection and try again.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task CompleteAuthenticationAsync(string verificationCode)
    {
        // Validate inputs
        if (string.IsNullOrWhiteSpace(verificationCode))
        {
            MessageBox.Show("Please enter the 6-digit verification code from SmugMug.",
                "Missing Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (_requestToken == null || _requestTokenSecret == null)
        {
            MessageBox.Show("Please click 'Authenticate with SmugMug' first to start the authentication process.",
                "Authentication Not Started", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        // Clean and validate verification code format
        var cleanCode = verificationCode.Trim().Replace("-", "").Replace(" ", "");
        if (cleanCode.Length != 6 || !cleanCode.All(char.IsDigit))
        {
            MessageBox.Show("The verification code should be exactly 6 digits. Please check and try again.",
                "Invalid Verification Code", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Completing authentication...";
            AddLogMessage($"Completing authentication with verification code: {cleanCode}");

            var accessTokenResponse = await _authService.CompleteAuthenticationAsync(
                cleanCode, _requestToken, _requestTokenSecret);

            UserNickname = accessTokenResponse.UserNickname;
            StatusMessage = $"Successfully authenticated as {UserNickname}";
            AddLogMessage($"✓ Authentication completed successfully for user: {UserNickname}");

            // Verify we have private access
            AddLogMessage("Verifying access permissions...");
            var hasPrivateAccess = await _apiClient.VerifyPrivateAccessAsync();
            if (hasPrivateAccess)
            {
                AddLogMessage("✓ Private access verified - can access your private photos");
            }
            else
            {
                AddLogMessage("⚠ Warning: Only public access detected - may not be able to access private photos");
                StatusMessage += " (Limited access - check permissions)";
            }

            // Clear temporary authentication state
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;

            AddLogMessage("✓ Authentication process completed successfully!");
        }
        catch (HttpRequestException httpEx) when (httpEx.Message.Contains("401"))
        {
            _logger.LogError(httpEx, "Authentication failed with 401 Unauthorized");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication failed: Invalid verification code or expired session");
            AddLogMessage("Please try the authentication process again from the beginning.");

            // Clear temporary state on 401 error
            _requestToken = null;
            _requestTokenSecret = null;
            _authorizationUrl = null;

            MessageBox.Show("Authentication failed. The verification code may be incorrect or expired.\n\nPlease click 'Authenticate with SmugMug' to start over.",
                "Authentication Failed", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to complete authentication");
            StatusMessage = "Authentication failed. Please try again.";
            AddLogMessage($"❌ Authentication completion error: {ex.Message}");

            // Show more detailed error information
            if (ex.InnerException != null)
            {
                AddLogMessage($"   Inner error: {ex.InnerException.Message}");
            }

            MessageBox.Show($"Authentication failed: {ex.Message}\n\nPlease try again or contact support if the problem persists.",
                "Authentication Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task LogoutAsync()
    {
        try
        {
            await _authService.ClearStoredCredentialsAsync();
            StatusMessage = "Logged out successfully";
            AddLogMessage("User logged out and credentials cleared");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to logout");
            AddLogMessage($"Logout error: {ex.Message}");
        }
    }

    [RelayCommand]
    private void SelectTargetDirectory()
    {
        var dialog = new Microsoft.Win32.OpenFolderDialog
        {
            Title = "Select Download Directory",
            Multiselect = false
        };

        if (dialog.ShowDialog() == true)
        {
            TargetDirectory = dialog.FolderName;
            AddLogMessage($"Target directory selected: {TargetDirectory}");
        }
    }

    [RelayCommand]
    private async Task TestApiConnectionAsync()
    {
        if (!IsAuthenticated)
        {
            MessageBox.Show("Please authenticate first.", "Not Authenticated", MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            StatusMessage = "Testing API connection...";
            AddLogMessage("Testing SmugMug API connection...");

            var user = await _apiClient.GetAuthenticatedUserAsync();
            var rootNode = await _apiClient.GetUserRootNodeAsync();
            var hasPrivateAccess = await _apiClient.VerifyPrivateAccessAsync();

            StatusMessage = "API connection successful";
            AddLogMessage($"API test successful. User: {user.Name}, Root node: {rootNode.Name}");

            var accessLevel = hasPrivateAccess ? "Full (Private)" : "Public Only";
            AddLogMessage($"Access level: {accessLevel}");

            var message = $"API connection successful!\nUser: {user.Name}\nRoot node: {rootNode.Name}\nAccess level: {accessLevel}";
            if (!hasPrivateAccess)
            {
                message += "\n\nWarning: Only public access detected. You may not be able to download private photos.";
            }

            MessageBox.Show(message, "API Test Results", MessageBoxButton.OK,
                hasPrivateAccess ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "API connection test failed");
            StatusMessage = "API connection failed";
            AddLogMessage($"API test failed: {ex.Message}");
            MessageBox.Show($"API connection failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
        }
    }

    [RelayCommand]
    private async Task StartDownloadAsync()
    {
        if (string.IsNullOrEmpty(TargetDirectory))
        {
            MessageBox.Show("Please select a target directory first.", "No Target Directory",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        if (!IsAuthenticated)
        {
            MessageBox.Show("Please authenticate first.", "Not Authenticated",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            IsOperationInProgress = true;
            UpdateDownloadButtonStates();
            AddLogMessage("Starting download process...");

            await _downloadManager.StartDownloadAsync(TargetDirectory);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Download failed");
            AddLogMessage($"Download failed: {ex.Message}");
            MessageBox.Show($"Download failed: {ex.Message}", "Download Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            IsOperationInProgress = false;
            UpdateDownloadButtonStates();
        }
    }

    [RelayCommand]
    private async Task PauseDownloadAsync()
    {
        try
        {
            await _downloadManager.PauseAsync();
            AddLogMessage("Download paused");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to pause download");
            AddLogMessage($"Failed to pause download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task ResumeDownloadAsync()
    {
        try
        {
            await _downloadManager.ResumeAsync();
            AddLogMessage("Download resumed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to resume download");
            AddLogMessage($"Failed to resume download: {ex.Message}");
        }
    }

    [RelayCommand]
    private async Task CancelDownloadAsync()
    {
        try
        {
            await _downloadManager.CancelAsync();
            AddLogMessage("Download cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel download");
            AddLogMessage($"Failed to cancel download: {ex.Message}");
        }
    }

    private void OnDownloadProgressUpdated(object? sender, DownloadProgressEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var stats = e.Statistics;
            OverallProgress = stats.OverallProgressPercentage;
            ProgressText = $"{stats.DownloadedPhotos}/{stats.TotalPhotos} photos ({stats.ProcessedAlbums}/{stats.TotalAlbums} albums)";

            if (stats.AverageDownloadSpeed.HasValue)
            {
                DownloadSpeed = FormatBytes(stats.AverageDownloadSpeed.Value) + "/s";
            }

            if (stats.EstimatedTimeRemaining.HasValue)
            {
                EstimatedTimeRemaining = FormatTimeSpan(stats.EstimatedTimeRemaining.Value);
            }

            if (!string.IsNullOrEmpty(e.CurrentOperation))
            {
                StatusMessage = e.CurrentOperation;
            }
        });
    }

    private void OnDownloadStatusChanged(object? sender, DownloadStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            StatusMessage = e.Message ?? e.NewStatus.ToString();
            AddLogMessage($"Download status: {e.NewStatus} - {e.Message}");
            UpdateDownloadButtonStates();

            if (e.NewStatus == DownloadStatus.Completed)
            {
                var summary = _downloadManager.GetDownloadSummary();
                ShowDownloadSummary(summary);
            }
        });
    }

    private void OnDownloadErrorOccurred(object? sender, DownloadErrorEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            var errorMessage = $"Error: {e.Exception.Message}";
            if (!string.IsNullOrEmpty(e.Context))
            {
                errorMessage = $"{e.Context}: {e.Exception.Message}";
            }
            AddLogMessage(errorMessage);
        });
    }

    private void UpdateDownloadButtonStates()
    {
        var status = _downloadManager.Status;
        CanStartDownload = IsAuthenticated && !string.IsNullOrEmpty(TargetDirectory) &&
                          (status == DownloadStatus.NotStarted || status == DownloadStatus.Completed ||
                           status == DownloadStatus.Cancelled || status == DownloadStatus.Error);
        CanPauseDownload = status == DownloadStatus.Downloading;
        CanResumeDownload = status == DownloadStatus.Paused;
        CanCancelDownload = status == DownloadStatus.Downloading || status == DownloadStatus.Paused;
    }

    private void OnAuthenticationStatusChanged(object? sender, AuthenticationStatusChangedEventArgs e)
    {
        Application.Current.Dispatcher.Invoke(() =>
        {
            IsAuthenticated = e.IsAuthenticated;
            UserNickname = e.UserNickname;
            UpdateAuthenticationStatus();

            if (e.Error != null)
            {
                AddLogMessage($"Authentication error: {e.Error.Message}");
            }
        });
    }

    private void UpdateAuthenticationStatus()
    {
        AuthenticationStatus = IsAuthenticated 
            ? $"Authenticated as {UserNickname ?? "Unknown"}"
            : "Not authenticated";
    }

    private void AddLogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        LogMessages.Add($"[{timestamp}] {message}");
        
        // Keep only the last 100 messages
        while (LogMessages.Count > 100)
        {
            LogMessages.RemoveAt(0);
        }
    }

    public async Task InitializeAsync()
    {
        try
        {
            AddLogMessage("Initializing application...");

            // Update download button states initially
            UpdateDownloadButtonStates();

            // Try to load stored credentials
            var hasStoredCredentials = await _authService.LoadStoredCredentialsAsync();
            if (hasStoredCredentials)
            {
                AddLogMessage("Stored credentials loaded successfully");
            }
            else
            {
                AddLogMessage("No stored credentials found");
            }

            AddLogMessage("Application initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize application");
            AddLogMessage($"Initialization error: {ex.Message}");
            StatusMessage = $"Initialization failed: {ex.Message}";

            // Show error to user
            MessageBox.Show($"Application initialization failed: {ex.Message}\n\nPlease check your configuration and try again.",
                "Initialization Error", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowDownloadSummary(DownloadSummary summary)
    {
        var message = $"Download completed!\n\n" +
                     $"Status: {summary.FinalStatus}\n" +
                     $"Total Photos: {summary.Statistics.TotalPhotos}\n" +
                     $"Downloaded: {summary.Statistics.DownloadedPhotos}\n" +
                     $"Failed: {summary.Statistics.FailedPhotos}\n" +
                     $"Total Time: {FormatTimeSpan(summary.TotalDuration)}\n" +
                     $"Average Speed: {(summary.Statistics.AverageDownloadSpeed.HasValue ? FormatBytes(summary.Statistics.AverageDownloadSpeed.Value) + "/s" : "N/A")}";

        if (summary.Errors.Any())
        {
            message += $"\n\nErrors: {summary.Errors.Count}";
        }

        MessageBox.Show(message, "Download Complete", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private static string FormatBytes(double bytes)
    {
        string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
        int counter = 0;
        decimal number = (decimal)bytes;
        while (Math.Round(number / 1024) >= 1)
        {
            number /= 1024;
            counter++;
        }
        return $"{number:n1} {suffixes[counter]}";
    }

    private static string FormatTimeSpan(TimeSpan timeSpan)
    {
        if (timeSpan.TotalDays >= 1)
            return $"{timeSpan.Days}d {timeSpan.Hours}h {timeSpan.Minutes}m";
        if (timeSpan.TotalHours >= 1)
            return $"{timeSpan.Hours}h {timeSpan.Minutes}m {timeSpan.Seconds}s";
        if (timeSpan.TotalMinutes >= 1)
            return $"{timeSpan.Minutes}m {timeSpan.Seconds}s";
        return $"{timeSpan.Seconds}s";
    }
}
