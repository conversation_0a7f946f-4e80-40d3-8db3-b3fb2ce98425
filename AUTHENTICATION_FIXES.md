# SmugMug Authentication Fixes

## Issues Fixed

### 1. UI Exception (NullReferenceException)
**Problem**: The application was experiencing a UI exception due to conflicting window initialization.

**Root Cause**: `App.xaml` had `StartupUri="Views/MainWindow.xaml"` which conflicted with the dependency injection-based window creation in `App.xaml.cs`.

**Fix**: Removed the `StartupUri` attribute from `App.xaml` to let the dependency injection system handle window creation properly.

### 2. Incorrect OAuth URLs
**Problem**: The OAuth URLs were pointing to `api.smugmug.com` instead of the correct `secure.smugmug.com`.

**Root Cause**: The URLs didn't match the official SmugMug OAuth tutorial specification.

**Fix**: Updated all OAuth URLs to use `secure.smugmug.com`:
- Request Token: `https://secure.smugmug.com/services/oauth/1.0a/getRequestToken`
- Authorize: `https://secure.smugmug.com/services/oauth/1.0a/authorize`
- Access Token: `https://secure.smugmug.com/services/oauth/1.0a/getAccessToken`

### 3. Enhanced Authentication Flow
**Improvements Made**:
- Better browser launching with fallback mechanisms
- Enhanced verification code validation (6 digits only)
- Improved error handling with specific 401 error messages
- Better user guidance with step-by-step instructions
- Enhanced logging with visual indicators (✓, ❌, ⚠)

## Files Modified

1. **src/Winmug/App.xaml** - Removed conflicting StartupUri
2. **src/Winmug.Core/Authentication/OAuthCredentials.cs** - Updated OAuth URLs
3. **src/Winmug/appsettings.json** - Updated OAuth URLs in configuration
4. **src/Winmug/ViewModels/MainWindowViewModel.cs** - Enhanced authentication flow
5. **src/Winmug.Core/Authentication/SmugMugAuthenticationService.cs** - Better error handling

## Testing the Fixes

1. Run `test-fixes.bat` to build and start the application
2. Click "Authenticate with SmugMug"
3. The browser should open automatically (or provide fallback instructions)
4. Complete the OAuth flow on SmugMug's website
5. Enter the 6-digit verification code
6. Authentication should complete successfully

## Expected Behavior

- ✅ No more UI exceptions on startup
- ✅ Browser opens automatically to SmugMug OAuth page
- ✅ Clear step-by-step instructions in the log
- ✅ Better error messages if something goes wrong
- ✅ Proper validation of verification codes
- ✅ Successful authentication with private access

## Reference

The OAuth implementation now follows the official SmugMug tutorial:
https://api.smugmug.com/api/v2/doc/tutorial/oauth/non-web.html
