using System.Security.Cryptography;
using System.Text;
using System.Web;

Console.WriteLine("SmugMug Authentication Debug Tool");
Console.WriteLine("=================================");
Console.WriteLine();

// Use the same credentials
var consumerKey = "R2nJdD3SWQVwM4qc8KX28vWxh9gpGrRL";
var consumerSecret = "FwgbRBSwQKMLHR36f5bw6ztNH8Qvf4SrVmDdTdVkmZrgbvTTM5SD9KPTczCBgTFT";

Console.WriteLine("Step 1: Testing complete OAuth flow with verification code...");
Console.WriteLine();

// Step 1: Get request token (we know this works)
var requestTokenResponse = await GetRequestTokenAsync(consumerKey, consumerSecret);
if (requestTokenResponse == null)
{
    Console.WriteLine("❌ Failed to get request token");
    return;
}

Console.WriteLine($"✓ Request token: {requestTokenResponse.Token.Substring(0, 8)}...");
Console.WriteLine($"✓ Request token secret: {requestTokenResponse.TokenSecret.Substring(0, 8)}...");
Console.WriteLine();

// Step 2: Exchange for access token using the verification code
Console.WriteLine("Step 2: Exchanging request token for access token...");
var verificationCode = "350538"; // The code you received
var accessTokenResponse = await GetAccessTokenAsync(
    consumerKey, consumerSecret, 
    requestTokenResponse.Token, requestTokenResponse.TokenSecret, 
    verificationCode);

if (accessTokenResponse == null)
{
    Console.WriteLine("❌ Failed to get access token");
    return;
}

Console.WriteLine($"✓ Access token: {accessTokenResponse.Token.Substring(0, 8)}...");
Console.WriteLine($"✓ Access token secret: {accessTokenResponse.TokenSecret.Substring(0, 8)}...");
Console.WriteLine($"✓ User nickname: {accessTokenResponse.UserNickname}");
Console.WriteLine();

// Step 3: Test API call with access token
Console.WriteLine("Step 3: Testing API call with access token...");
var apiTestResult = await TestApiCallAsync(consumerKey, consumerSecret, 
    accessTokenResponse.Token, accessTokenResponse.TokenSecret);

if (apiTestResult)
{
    Console.WriteLine("✅ SUCCESS! Authentication flow is working correctly.");
    Console.WriteLine();
    Console.WriteLine("The issue is likely in the application's token storage/retrieval.");
    Console.WriteLine("Check:");
    Console.WriteLine("1. Is the access token being saved to Windows Credential Manager?");
    Console.WriteLine("2. Is the token being loaded correctly on app startup?");
    Console.WriteLine("3. Is the CreateAuthenticatedRequest method using the right token?");
}
else
{
    Console.WriteLine("❌ API call failed even with fresh tokens");
    Console.WriteLine("This suggests an issue with the OAuth signature generation for API calls");
}

static async Task<RequestTokenResponse?> GetRequestTokenAsync(string consumerKey, string consumerSecret)
{
    try
    {
        var parameters = new Dictionary<string, string>
        {
            ["oauth_callback"] = "oob",
            ["oauth_consumer_key"] = consumerKey,
            ["oauth_nonce"] = Guid.NewGuid().ToString("N"),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
            ["oauth_version"] = "1.0"
        };

        var signature = GenerateOAuthSignature("POST", "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken", parameters, consumerSecret);
        parameters["oauth_signature"] = signature;

        var authHeader = CreateAuthorizationHeader(parameters);

        using var httpClient = new HttpClient();
        var request = new HttpRequestMessage(HttpMethod.Post, "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken");
        request.Headers.Add("Authorization", authHeader);
        request.Headers.Add("User-Agent", "AuthDebug/1.0");

        var response = await httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            Console.WriteLine($"Request token failed: {response.StatusCode} {responseContent}");
            return null;
        }

        var responseParams = ParseQueryString(responseContent);
        return new RequestTokenResponse
        {
            Token = responseParams["oauth_token"] ?? "",
            TokenSecret = responseParams["oauth_token_secret"] ?? ""
        };
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Request token error: {ex.Message}");
        return null;
    }
}

static async Task<AccessTokenResponse?> GetAccessTokenAsync(string consumerKey, string consumerSecret, 
    string requestToken, string requestTokenSecret, string verificationCode)
{
    try
    {
        var parameters = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = consumerKey,
            ["oauth_nonce"] = Guid.NewGuid().ToString("N"),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
            ["oauth_token"] = requestToken,
            ["oauth_verifier"] = verificationCode,
            ["oauth_version"] = "1.0"
        };

        var signature = GenerateOAuthSignature("POST", "https://secure.smugmug.com/services/oauth/1.0a/getAccessToken", 
            parameters, consumerSecret, requestTokenSecret);
        parameters["oauth_signature"] = signature;

        var authHeader = CreateAuthorizationHeader(parameters);

        using var httpClient = new HttpClient();
        var request = new HttpRequestMessage(HttpMethod.Post, "https://secure.smugmug.com/services/oauth/1.0a/getAccessToken");
        request.Headers.Add("Authorization", authHeader);
        request.Headers.Add("User-Agent", "AuthDebug/1.0");

        var response = await httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        Console.WriteLine($"Access token response: {response.StatusCode}");
        Console.WriteLine($"Response content: {responseContent}");

        if (!response.IsSuccessStatusCode)
        {
            Console.WriteLine($"Access token failed: {response.StatusCode} {responseContent}");
            return null;
        }

        var responseParams = ParseQueryString(responseContent);
        return new AccessTokenResponse
        {
            Token = responseParams["oauth_token"] ?? "",
            TokenSecret = responseParams["oauth_token_secret"] ?? "",
            UserNickname = responseParams.GetValueOrDefault("oauth_screen_name")
        };
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Access token error: {ex.Message}");
        return null;
    }
}

static async Task<bool> TestApiCallAsync(string consumerKey, string consumerSecret, 
    string accessToken, string accessTokenSecret)
{
    try
    {
        var url = "https://api.smugmug.com/api/v2!authuser";
        var parameters = new Dictionary<string, string>
        {
            ["oauth_consumer_key"] = consumerKey,
            ["oauth_nonce"] = Guid.NewGuid().ToString("N"),
            ["oauth_signature_method"] = "HMAC-SHA1",
            ["oauth_timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
            ["oauth_token"] = accessToken,
            ["oauth_version"] = "1.0"
        };

        var signature = GenerateOAuthSignature("GET", url, parameters, consumerSecret, accessTokenSecret);
        parameters["oauth_signature"] = signature;

        var authHeader = CreateAuthorizationHeader(parameters);

        using var httpClient = new HttpClient();
        var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Add("Authorization", authHeader);
        request.Headers.Add("Accept", "application/json");
        request.Headers.Add("User-Agent", "AuthDebug/1.0");

        var response = await httpClient.SendAsync(request);
        var responseContent = await response.Content.ReadAsStringAsync();

        Console.WriteLine($"API call response: {response.StatusCode}");
        Console.WriteLine($"Response content: {responseContent.Substring(0, Math.Min(200, responseContent.Length))}...");

        return response.IsSuccessStatusCode;
    }
    catch (Exception ex)
    {
        Console.WriteLine($"API call error: {ex.Message}");
        return false;
    }
}

static string GenerateOAuthSignature(string httpMethod, string url, Dictionary<string, string> parameters, 
    string consumerSecret, string? tokenSecret = null)
{
    var normalizedUrl = NormalizeUrl(url);
    var normalizedParameters = CreateParameterString(parameters);
    var signatureBaseString = $"{httpMethod.ToUpperInvariant()}&{UrlEncode(normalizedUrl)}&{UrlEncode(normalizedParameters)}";
    
    var signingKey = $"{UrlEncode(consumerSecret)}&{UrlEncode(tokenSecret ?? string.Empty)}";
    
    var keyBytes = Encoding.UTF8.GetBytes(signingKey);
    var dataBytes = Encoding.UTF8.GetBytes(signatureBaseString);
    
    using var hmac = new HMACSHA1(keyBytes);
    var hashBytes = hmac.ComputeHash(dataBytes);
    return Convert.ToBase64String(hashBytes);
}

static string NormalizeUrl(string url)
{
    var uri = new Uri(url);
    var normalizedUrl = $"{uri.Scheme.ToLowerInvariant()}://{uri.Host.ToLowerInvariant()}";
    
    if ((uri.Scheme.ToLowerInvariant() == "http" && uri.Port != 80) ||
        (uri.Scheme.ToLowerInvariant() == "https" && uri.Port != 443))
    {
        normalizedUrl += $":{uri.Port}";
    }
    
    normalizedUrl += uri.AbsolutePath;
    return normalizedUrl;
}

static string CreateParameterString(Dictionary<string, string> parameters)
{
    var sortedParameters = parameters
        .OrderBy(kvp => kvp.Key)
        .ThenBy(kvp => kvp.Value)
        .Select(kvp => $"{UrlEncode(kvp.Key)}={UrlEncode(kvp.Value)}")
        .ToArray();
    
    return string.Join("&", sortedParameters);
}

static string CreateAuthorizationHeader(Dictionary<string, string> parameters)
{
    var headerParams = parameters
        .Where(kvp => kvp.Key.StartsWith("oauth_"))
        .OrderBy(kvp => kvp.Key)
        .Select(kvp => $"{UrlEncode(kvp.Key)}=\"{UrlEncode(kvp.Value)}\"");

    return $"OAuth {string.Join(", ", headerParams)}";
}

static string UrlEncode(string value)
{
    if (string.IsNullOrEmpty(value))
        return string.Empty;

    var encoded = HttpUtility.UrlEncode(value, Encoding.UTF8);
    var result = encoded.Replace("+", "%20").Replace("*", "%2A").Replace("%7E", "~");
    
    return System.Text.RegularExpressions.Regex.Replace(result, @"%[0-9a-f]{2}", 
        match => match.Value.ToUpperInvariant());
}

static Dictionary<string, string> ParseQueryString(string queryString)
{
    var result = new Dictionary<string, string>();
    var pairs = queryString.Split('&');
    
    foreach (var pair in pairs)
    {
        var keyValue = pair.Split('=', 2);
        if (keyValue.Length == 2)
        {
            result[HttpUtility.UrlDecode(keyValue[0])] = HttpUtility.UrlDecode(keyValue[1]);
        }
    }
    
    return result;
}

public class RequestTokenResponse
{
    public string Token { get; set; } = string.Empty;
    public string TokenSecret { get; set; } = string.Empty;
}

public class AccessTokenResponse
{
    public string Token { get; set; } = string.Empty;
    public string TokenSecret { get; set; } = string.Empty;
    public string? UserNickname { get; set; }
}
