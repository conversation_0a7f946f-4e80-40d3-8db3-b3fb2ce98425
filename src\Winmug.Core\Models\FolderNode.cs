using System.Collections.ObjectModel;

namespace Winmug.Core.Models;

/// <summary>
/// Represents a folder/node in the SmugMug hierarchy
/// </summary>
public class FolderNode
{
    public string NodeId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty; // "Folder", "Album", etc.
    public string UrlName { get; set; } = string.Empty;
    public string ParentNodeId { get; set; } = string.Empty;
    public string FullPath { get; set; } = string.Empty;
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public bool HasChildren { get; set; }
    public bool IsExpanded { get; set; }
    public bool IsSelected { get; set; }
    
    /// <summary>
    /// Child folders and albums
    /// </summary>
    public ObservableCollection<FolderNode> Children { get; set; } = new();
    
    /// <summary>
    /// Albums directly in this folder
    /// </summary>
    public ObservableCollection<AlbumInfo> Albums { get; set; } = new();
    
    /// <summary>
    /// Total number of images in this folder and all subfolders
    /// </summary>
    public int TotalImageCount { get; set; }
    
    /// <summary>
    /// Total estimated size of all images in this folder and subfolders
    /// </summary>
    public long TotalEstimatedSizeBytes { get; set; }
    
    /// <summary>
    /// Human-readable total size
    /// </summary>
    public string TotalEstimatedSize => FormatBytes(TotalEstimatedSizeBytes);
    
    /// <summary>
    /// Display text for the tree view
    /// </summary>
    public string DisplayText => $"{Name} ({TotalImageCount} images, {TotalEstimatedSize})";
    
    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
