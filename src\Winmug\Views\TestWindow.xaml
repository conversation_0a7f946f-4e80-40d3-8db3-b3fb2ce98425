<Window x:Class="Winmug.Views.TestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Winmug Test Window" Height="300" Width="400"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="20">
        <StackPanel>
            <TextBlock Text="Winmug Test Window" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
            <TextBlock Text="If you can see this window, the basic WPF setup is working." TextWrapping="Wrap" Margin="0,0,0,10"/>
            <Button Content="Test Button" Width="100" Height="30" Margin="0,10,0,0" Click="TestButton_Click"/>
            <TextBlock x:Name="StatusText" Text="Ready" Margin="0,20,0,0" FontWeight="Bold"/>
        </StackPanel>
    </Grid>
</Window>
