using Microsoft.Extensions.Logging;
using System.Net.Http;
using System.Text;

namespace Winmug.Core.Authentication;

/// <summary>
/// OAuth testing utility to validate credentials and signature generation
/// </summary>
public class OAuthTester
{
    private readonly HttpClient _httpClient;
    private readonly ILogger _logger;

    public OAuthTester(HttpClient httpClient, ILogger logger)
    {
        _httpClient = httpClient;
        _logger = logger;
    }

    /// <summary>
    /// Test OAuth signature generation with known good values
    /// </summary>
    public async Task<bool> TestOAuthSignatureAsync(string consumerKey, string consumerSecret)
    {
        try
        {
            _logger.LogInformation("Testing OAuth signature generation...");

            // Test with a simple request to validate signature generation
            var parameters = new Dictionary<string, string>
            {
                ["oauth_callback"] = "oob",
                ["oauth_consumer_key"] = consumerKey,
                ["oauth_nonce"] = "test-nonce-12345",
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = "1234567890",
                ["oauth_version"] = "1.0"
            };

            var url = "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken";
            
            _logger.LogInformation("Test parameters:");
            foreach (var param in parameters)
            {
                _logger.LogInformation("  {Key} = {Value}", param.Key, param.Value);
            }

            var signature = OAuthSignatureGenerator.GenerateSignature("POST", url, parameters, consumerSecret);
            _logger.LogInformation("Generated signature: {Signature}", signature);

            // Test the actual request
            parameters["oauth_signature"] = signature;
            var authHeader = CreateAuthorizationHeader(parameters);
            _logger.LogInformation("Authorization header: {AuthHeader}", authHeader);

            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Authorization", authHeader);

            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("Response status: {StatusCode}", response.StatusCode);
            _logger.LogInformation("Response content: {Content}", responseContent);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "OAuth signature test failed");
            return false;
        }
    }

    /// <summary>
    /// Test with current timestamp and nonce
    /// </summary>
    public async Task<bool> TestRealOAuthRequestAsync(string consumerKey, string consumerSecret)
    {
        try
        {
            _logger.LogInformation("Testing real OAuth request...");

            var parameters = new Dictionary<string, string>
            {
                ["oauth_callback"] = "oob",
                ["oauth_consumer_key"] = consumerKey,
                ["oauth_nonce"] = OAuthSignatureGenerator.GenerateNonce(),
                ["oauth_signature_method"] = "HMAC-SHA1",
                ["oauth_timestamp"] = OAuthSignatureGenerator.GenerateTimestamp(),
                ["oauth_version"] = "1.0"
            };

            var url = "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken";
            
            // Log the request details
            OAuthDebugger.LogOAuthRequest(_logger, "POST", url, parameters, consumerSecret);

            var signature = OAuthSignatureGenerator.GenerateSignature("POST", url, parameters, consumerSecret);
            parameters["oauth_signature"] = signature;

            var authHeader = CreateAuthorizationHeader(parameters);
            var request = new HttpRequestMessage(HttpMethod.Post, url);
            request.Headers.Add("Authorization", authHeader);

            _logger.LogInformation("Sending request to SmugMug...");
            var response = await _httpClient.SendAsync(request);
            var responseContent = await response.Content.ReadAsStringAsync();

            _logger.LogInformation("=== RESPONSE DETAILS ===");
            _logger.LogInformation("Response status: {StatusCode} {ReasonPhrase}", response.StatusCode, response.ReasonPhrase);
            _logger.LogInformation("Response headers:");
            foreach (var header in response.Headers)
            {
                _logger.LogInformation("  {Name}: {Value}", header.Key, string.Join(", ", header.Value));
            }
            _logger.LogInformation("Response content: {Content}", responseContent);
            _logger.LogInformation("=== END RESPONSE ===");

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Request failed. This could indicate:");

                if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                {
                    _logger.LogError("401 Unauthorized - Possible causes:");
                    _logger.LogError("1. API credentials are invalid or expired");
                    _logger.LogError("2. OAuth signature is incorrect");
                    _logger.LogError("3. System clock is out of sync (check timestamp)");
                    _logger.LogError("4. API key doesn't have proper permissions");
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
                {
                    _logger.LogError("403 Forbidden - API key may not have required permissions");
                }
                else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
                {
                    _logger.LogError("400 Bad Request - OAuth parameters may be malformed");
                }
                else
                {
                    _logger.LogError("HTTP {StatusCode} - Check network connectivity and API endpoints", response.StatusCode);
                }

                _logger.LogError("Response body may contain more details: {ResponseContent}", responseContent);
            }

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Real OAuth request test failed");
            return false;
        }
    }

    private static string CreateAuthorizationHeader(Dictionary<string, string> parameters)
    {
        var headerParams = parameters
            .Where(kvp => kvp.Key.StartsWith("oauth_"))
            .OrderBy(kvp => kvp.Key)
            .Select(kvp => $"{OAuthSignatureGenerator.UrlEncode(kvp.Key)}=\"{OAuthSignatureGenerator.UrlEncode(kvp.Value)}\"");

        return $"OAuth {string.Join(", ", headerParams)}";
    }
}
