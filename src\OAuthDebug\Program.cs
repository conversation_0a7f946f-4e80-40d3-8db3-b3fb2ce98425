using Microsoft.Extensions.Configuration;
using System.Security.Cryptography;
using System.Text;
using System.Web;

Console.WriteLine("SmugMug OAuth Debug Tool");
Console.WriteLine("========================");
Console.WriteLine();

// Use the credentials directly (from your appsettings.json)
var consumerKey = "R2nJdD3SWQVwM4qc8KX28vWxh9gpGrRL";
var consumerSecret = "FwgbRBSwQKMLHR36f5bw6ztNH8Qvf4SrVmDdTdVkmZrgbvTTM5SD9KPTczCBgTFT";

if (string.IsNullOrEmpty(consumerKey) || string.IsNullOrEmpty(consumerSecret))
{
    Console.WriteLine("❌ Error: Could not read API credentials from configuration");
    return;
}

Console.WriteLine($"Consumer Key: {consumerKey.Substring(0, 8)}...");
Console.WriteLine($"Consumer Secret: {consumerSecret.Substring(0, 8)}...");
Console.WriteLine();

// OAuth parameters
var parameters = new Dictionary<string, string>
{
    ["oauth_callback"] = "oob",
    ["oauth_consumer_key"] = consumerKey,
    ["oauth_nonce"] = Guid.NewGuid().ToString("N"),
    ["oauth_signature_method"] = "HMAC-SHA1",
    ["oauth_timestamp"] = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(),
    ["oauth_version"] = "1.0"
};

var url = "https://secure.smugmug.com/services/oauth/1.0a/getRequestToken";

Console.WriteLine("OAuth Parameters:");
foreach (var param in parameters.OrderBy(p => p.Key))
{
    var value = param.Key.Contains("secret") ? "***" : param.Value;
    Console.WriteLine($"  {param.Key} = {value}");
}
Console.WriteLine();

// Generate signature
try
{
    var signature = GenerateOAuthSignature("POST", url, parameters, consumerSecret);
    parameters["oauth_signature"] = signature;
    
    Console.WriteLine($"Generated Signature: {signature.Substring(0, Math.Min(20, signature.Length))}...");
    Console.WriteLine();
    
    // Create authorization header
    var authHeader = CreateAuthorizationHeader(parameters);
    Console.WriteLine($"Authorization Header: {authHeader.Substring(0, Math.Min(80, authHeader.Length))}...");
    Console.WriteLine();
    
    // Make HTTP request
    using var httpClient = new HttpClient();
    httpClient.DefaultRequestHeaders.Add("User-Agent", "Winmug-Debug/1.0");
    
    var request = new HttpRequestMessage(HttpMethod.Post, url);
    request.Headers.Add("Authorization", authHeader);
    
    Console.WriteLine($"Making request to: {url}");
    Console.WriteLine();
    
    var response = await httpClient.SendAsync(request);
    var responseContent = await response.Content.ReadAsStringAsync();
    
    Console.WriteLine("=== RESPONSE ===");
    Console.WriteLine($"Status: {response.StatusCode} {response.ReasonPhrase}");
    Console.WriteLine($"Content: {responseContent}");
    Console.WriteLine();
    
    if (!response.IsSuccessStatusCode)
    {
        Console.WriteLine("❌ REQUEST FAILED");
        Console.WriteLine();
        Console.WriteLine("Possible causes:");
        
        if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            Console.WriteLine("• Invalid API credentials");
            Console.WriteLine("• OAuth signature is incorrect");
            Console.WriteLine("• System clock is out of sync");
            Console.WriteLine("• API key doesn't have OAuth permissions");
        }
        else if (response.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            Console.WriteLine("• API key doesn't have required permissions");
        }
        else if (response.StatusCode == System.Net.HttpStatusCode.BadRequest)
        {
            Console.WriteLine("• OAuth parameters are malformed");
        }
        else
        {
            Console.WriteLine("• Network connectivity issues");
            Console.WriteLine("• SmugMug API endpoint problems");
        }
        
        Console.WriteLine();
        Console.WriteLine("Debug Information:");
        Console.WriteLine($"• Current UTC Time: {DateTime.UtcNow}");
        Console.WriteLine($"• Timestamp Used: {parameters["oauth_timestamp"]}");
        Console.WriteLine($"• Nonce: {parameters["oauth_nonce"]}");
        Console.WriteLine($"• Signature Base String: POST&{UrlEncode(url)}&{UrlEncode(CreateParameterString(parameters.Where(p => p.Key != "oauth_signature").ToDictionary(p => p.Key, p => p.Value)))}");
    }
    else
    {
        Console.WriteLine("✅ SUCCESS! OAuth request token request worked!");
    }
}
catch (Exception ex)
{
    Console.WriteLine($"❌ Exception: {ex.Message}");
    Console.WriteLine($"Stack trace: {ex.StackTrace}");
}

static string GenerateOAuthSignature(string httpMethod, string url, Dictionary<string, string> parameters, string consumerSecret, string? tokenSecret = null)
{
    // Create signature base string
    var normalizedUrl = NormalizeUrl(url);
    var normalizedParameters = CreateParameterString(parameters);
    var signatureBaseString = $"{httpMethod.ToUpperInvariant()}&{UrlEncode(normalizedUrl)}&{UrlEncode(normalizedParameters)}";
    
    // Create signing key
    var signingKey = $"{UrlEncode(consumerSecret)}&{UrlEncode(tokenSecret ?? string.Empty)}";
    
    // Generate HMAC-SHA1 signature
    var keyBytes = Encoding.UTF8.GetBytes(signingKey);
    var dataBytes = Encoding.UTF8.GetBytes(signatureBaseString);
    
    using var hmac = new HMACSHA1(keyBytes);
    var hashBytes = hmac.ComputeHash(dataBytes);
    return Convert.ToBase64String(hashBytes);
}

static string NormalizeUrl(string url)
{
    var uri = new Uri(url);
    var normalizedUrl = $"{uri.Scheme.ToLowerInvariant()}://{uri.Host.ToLowerInvariant()}";
    
    if ((uri.Scheme.ToLowerInvariant() == "http" && uri.Port != 80) ||
        (uri.Scheme.ToLowerInvariant() == "https" && uri.Port != 443))
    {
        normalizedUrl += $":{uri.Port}";
    }
    
    normalizedUrl += uri.AbsolutePath;
    return normalizedUrl;
}

static string CreateParameterString(Dictionary<string, string> parameters)
{
    var sortedParameters = parameters
        .OrderBy(kvp => kvp.Key)
        .ThenBy(kvp => kvp.Value)
        .Select(kvp => $"{UrlEncode(kvp.Key)}={UrlEncode(kvp.Value)}")
        .ToArray();
    
    return string.Join("&", sortedParameters);
}

static string CreateAuthorizationHeader(Dictionary<string, string> parameters)
{
    var headerParams = parameters
        .Where(kvp => kvp.Key.StartsWith("oauth_"))
        .OrderBy(kvp => kvp.Key)
        .Select(kvp => $"{UrlEncode(kvp.Key)}=\"{UrlEncode(kvp.Value)}\"");

    return $"OAuth {string.Join(", ", headerParams)}";
}

static string UrlEncode(string value)
{
    if (string.IsNullOrEmpty(value))
        return string.Empty;

    var encoded = HttpUtility.UrlEncode(value, Encoding.UTF8);
    var result = encoded.Replace("+", "%20").Replace("*", "%2A").Replace("%7E", "~");

    // Convert hex digits to uppercase (OAuth spec requirement)
    return System.Text.RegularExpressions.Regex.Replace(result, @"%[0-9a-f]{2}",
        match => match.Value.ToUpperInvariant());
}
