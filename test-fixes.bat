@echo off
echo Testing Winmug fixes...
echo.
echo 1. Building the application...
cd src\Winmug
dotnet build --verbosity quiet
if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo.
echo 2. Starting the application...
echo   - The UI exception should be fixed
echo   - OAuth URLs now use secure.smugmug.com (correct)
echo   - Better error handling and user guidance
echo   - New "Test OAuth" button for diagnostics
echo.
echo TESTING INSTRUCTIONS:
echo 1. Click "Test OAuth" to diagnose OAuth signature issues
echo 2. Check the logs for detailed OAuth debugging information
echo 3. If OAuth test passes, try "Authenticate with SmugMug"
echo.
echo Press any key to start the application...
pause > nul

dotnet run
