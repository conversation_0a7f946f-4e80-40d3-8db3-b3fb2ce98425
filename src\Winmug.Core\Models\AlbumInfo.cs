namespace Winmug.Core.Models;

/// <summary>
/// Represents a SmugMug album with metadata
/// </summary>
public class AlbumInfo
{
    public string AlbumKey { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string UrlName { get; set; } = string.Empty;
    public int ImageCount { get; set; }
    public long EstimatedSizeBytes { get; set; }
    public DateTime DateCreated { get; set; }
    public DateTime DateModified { get; set; }
    public string Privacy { get; set; } = string.Empty;
    public bool IsPublic { get; set; }
    public string NodeId { get; set; } = string.Empty;
    public string ParentNodeId { get; set; } = string.Empty;
    public string FullPath { get; set; } = string.Empty;
    
    /// <summary>
    /// Human-readable size estimate
    /// </summary>
    public string EstimatedSize => FormatBytes(EstimatedSizeBytes);
    
    /// <summary>
    /// Format bytes into human-readable string
    /// </summary>
    private static string FormatBytes(long bytes)
    {
        if (bytes == 0) return "0 B";
        
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        int order = 0;
        double size = bytes;
        
        while (size >= 1024 && order < sizes.Length - 1)
        {
            order++;
            size /= 1024;
        }
        
        return $"{size:0.##} {sizes[order]}";
    }
}
