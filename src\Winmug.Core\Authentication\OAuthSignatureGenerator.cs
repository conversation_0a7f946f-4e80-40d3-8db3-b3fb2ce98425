using System.Security.Cryptography;
using System.Text;
using System.Web;

namespace Winmug.Core.Authentication;

/// <summary>
/// Generates OAuth 1.0a signatures for SmugMug API requests
/// </summary>
public static class OAuthSignatureGenerator
{
    /// <summary>
    /// Generates an OAuth 1.0a signature for the given request
    /// </summary>
    public static string GenerateSignature(
        string httpMethod,
        string url,
        Dictionary<string, string> parameters,
        string consumerSecret,
        string? tokenSecret = null)
    {
        // Create the signature base string
        var signatureBaseString = CreateSignatureBaseString(httpMethod, url, parameters);
        
        // Create the signing key
        var signingKey = CreateSigningKey(consumerSecret, tokenSecret);
        
        // Generate the signature
        return GenerateHmacSha1Signature(signatureBaseString, signingKey);
    }

    /// <summary>
    /// Creates the OAuth signature base string
    /// </summary>
    private static string CreateSignatureBaseString(
        string httpMethod,
        string url,
        Dictionary<string, string> parameters)
    {
        // Normalize the URL (remove query parameters, convert to lowercase scheme and host)
        var normalizedUrl = NormalizeUrl(url);
        
        // Sort and encode parameters
        var normalizedParameters = NormalizeParameters(parameters);
        
        // Combine into signature base string
        var signatureBaseString = $"{httpMethod.ToUpperInvariant()}&{UrlEncode(normalizedUrl)}&{UrlEncode(normalizedParameters)}";
        
        return signatureBaseString;
    }

    /// <summary>
    /// Normalizes the URL by removing query parameters and converting scheme/host to lowercase
    /// </summary>
    private static string NormalizeUrl(string url)
    {
        var uri = new Uri(url);
        var normalizedUrl = $"{uri.Scheme.ToLowerInvariant()}://{uri.Host.ToLowerInvariant()}";
        
        if ((uri.Scheme == "http" && uri.Port != 80) || (uri.Scheme == "https" && uri.Port != 443))
        {
            normalizedUrl += $":{uri.Port}";
        }
        
        normalizedUrl += uri.AbsolutePath;
        
        return normalizedUrl;
    }

    /// <summary>
    /// Normalizes OAuth parameters by sorting and encoding them
    /// </summary>
    private static string NormalizeParameters(Dictionary<string, string> parameters)
    {
        var sortedParameters = parameters
            .OrderBy(kvp => kvp.Key)
            .ThenBy(kvp => kvp.Value)
            .Select(kvp => $"{UrlEncode(kvp.Key)}={UrlEncode(kvp.Value)}")
            .ToArray();
        
        return string.Join("&", sortedParameters);
    }

    /// <summary>
    /// Creates the signing key for OAuth signature generation
    /// </summary>
    private static string CreateSigningKey(string consumerSecret, string? tokenSecret)
    {
        return $"{UrlEncode(consumerSecret)}&{UrlEncode(tokenSecret ?? string.Empty)}";
    }

    /// <summary>
    /// Generates HMAC-SHA1 signature
    /// </summary>
    private static string GenerateHmacSha1Signature(string signatureBaseString, string signingKey)
    {
        var keyBytes = Encoding.UTF8.GetBytes(signingKey);
        var dataBytes = Encoding.UTF8.GetBytes(signatureBaseString);
        
        using var hmac = new HMACSHA1(keyBytes);
        var hashBytes = hmac.ComputeHash(dataBytes);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// URL encodes a string according to OAuth specification
    /// </summary>
    public static string UrlEncode(string value)
    {
        if (string.IsNullOrEmpty(value))
            return string.Empty;

        // Use HttpUtility.UrlEncode and then replace + with %20 and ~ with %7E
        var encoded = HttpUtility.UrlEncode(value, Encoding.UTF8);
        var result = encoded.Replace("+", "%20").Replace("*", "%2A").Replace("%7E", "~");

        // Convert hex digits to uppercase (OAuth spec requirement)
        return System.Text.RegularExpressions.Regex.Replace(result, @"%[0-9a-f]{2}",
            match => match.Value.ToUpperInvariant());
    }

    /// <summary>
    /// Generates a random nonce for OAuth requests
    /// </summary>
    public static string GenerateNonce()
    {
        return Guid.NewGuid().ToString("N");
    }

    /// <summary>
    /// Generates a timestamp for OAuth requests
    /// </summary>
    public static string GenerateTimestamp()
    {
        var unixEpoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        var timestamp = (long)(DateTime.UtcNow - unixEpoch).TotalSeconds;
        return timestamp.ToString();
    }
}
