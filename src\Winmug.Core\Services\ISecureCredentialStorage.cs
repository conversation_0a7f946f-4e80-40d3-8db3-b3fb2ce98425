namespace Winmug.Core.Services;

/// <summary>
/// Service for securely storing and retrieving OAuth credentials
/// </summary>
public interface ISecureCredentialStorage
{
    /// <summary>
    /// Stores OAuth credentials securely
    /// </summary>
    /// <param name="accessToken">OAuth access token</param>
    /// <param name="accessTokenSecret">OAuth access token secret</param>
    /// <param name="userNickname">SmugMug user nickname (optional)</param>
    Task StoreCredentialsAsync(string accessToken, string accessTokenSecret, string? userNickname = null);

    /// <summary>
    /// Retrieves stored OAuth credentials
    /// </summary>
    /// <returns>Stored credentials or null if not found</returns>
    Task<StoredCredentials?> RetrieveCredentialsAsync();

    /// <summary>
    /// Clears stored OAuth credentials
    /// </summary>
    Task ClearCredentialsAsync();

    /// <summary>
    /// Checks if credentials are stored
    /// </summary>
    /// <returns>True if credentials are stored</returns>
    Task<bool> HasStoredCredentialsAsync();
}

/// <summary>
/// Represents stored OAuth credentials
/// </summary>
public class StoredCredentials
{
    public string AccessToken { get; set; } = string.Empty;
    public string AccessTokenSecret { get; set; } = string.Empty;
    public string? UserNickname { get; set; }
    public DateTime StoredAt { get; set; }
}
